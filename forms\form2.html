<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON>ui<PERSON> ser Partner</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Sora:wght@100..800&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              orange: "#ff8623",
              textGray: "#717e8a",
              inputGray: "#e9eaed",
            },
          },
          fontFamily: {
            sora: ["Sora", "sans-serif"],
          },
        },
      };
    </script>
  </head>
  <body>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <form
      id="__vtigerWebForm3d27269d524ef8d15b77f4019b6b981d"
      class="__vtigerWebForm"
      name="Prospectos Partner WWW"
      action="https://microsipmtz.mantic360.net/modules/Webforms/capture.php"
      method="post"
      accept-charset="utf-8"
      enctype="multipart/form-data"
    >
      <input
        type="hidden"
        name="__vtrftk"
        value="sid:01b11ddc87fc40c7acf5ca6deeb0bdad981d740d,1731437983"
      /><input
        type="hidden"
        name="publicid"
        value="3d27269d524ef8d15b77f4019b6b981d"
      /><input type="hidden" name="urlencodeenable" value="1" /><input
        type="hidden"
        name="name"
        value="Prospectos Partner WWW"
      />
      <table class="w-full font-sora">
        <tbody>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">Nombres*</label>
            </td>
            <td>
              <input
                type="text"
                name="firstname"
                data-label=""
                value=""
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">Apellidos*</label>
            </td>
            <td>
              <input
                type="text"
                name="lastname"
                data-label=""
                value=""
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">Tel o Cel*</label>
            </td>
            <td>
              <input
                type="tel"
                name="cf_1129"
                data-label=""
                value=""
                pattern="[0-9]{10}"
                title="Por favor ingrese un número telefónico válido de 10 dígitos"
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">E-mail*</label>
            </td>
            <td>
              <input
                type="email"
                name="email"
                data-label=""
                value=""
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">Ciudad*</label>
            </td>
            <td>
              <input
                type="text"
                name="cf_1151"
                data-label=""
                value=""
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray"
                >Municipio o Delegación*</label
              >
            </td>
            <td>
              <input
                type="text"
                name="cf_1321"
                data-label=""
                value=""
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              />
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray">Estado*</label>
            </td>
            <td>
              <select
                name="cf_1315"
                data-label="label:Estado+sitio+web"
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 appearance-none cursor-pointer"
              >
                <option value="">Seleccione un estado</option>
                <option value="Aguascalientes">Aguascalientes</option>
                <option value="Baja California">Baja California</option>
                <option value="Baja California Sur">Baja California Sur</option>
                <option value="Campeche">Campeche</option>
                <option value="Coahuila">Coahuila</option>
                <option value="Colima">Colima</option>
                <option value="Chiapas">Chiapas</option>
                <option value="Chihuahua">Chihuahua</option>
                <option value="CDMX">CDMX</option>
                <option value="Durango">Durango</option>
                <option value="Guanajuato">Guanajuato</option>
                <option value="Guerrero">Guerrero</option>
                <option value="Hidalgo">Hidalgo</option>
                <option value="Jalisco">Jalisco</option>
                <option value="Edo. de México">Edo. de México</option>
                <option value="Michoacan">Michoacan</option>
                <option value="Morelos">Morelos</option>
                <option value="Nayarit">Nayarit</option>
                <option value="Nuevo León">Nuevo León</option>
                <option value="Oaxaca">Oaxaca</option>
                <option value="Puebla">Puebla</option>
                <option value="Querétaro">Querétaro</option>
                <option value="Quintana Roo">Quintana Roo</option>
                <option value="San Luis Potosí">San Luis Potosí</option>
                <option value="Sinaloa">Sinaloa</option>
                <option value="Sonora">Sonora</option>
                <option value="Tabasco">Tabasco</option>
                <option value="Tamaulipas">Tamaulipas</option>
                <option value="Tlaxcala">Tlaxcala</option>
                <option value="Veracruz">Veracruz</option>
                <option value="Yucatán">Yucatán</option>
                <option value="Zacatecas">Zacatecas</option>
              </select>
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray"
                >¿Por qué te interesa ser Microsip Partner?*</label
              >
            </td>
            <td>
              <textarea
                name="cf_1323"
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 resize-none h-24 min-h-24 max-h-24"
              ></textarea>
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray"
                >¿Cuáles ERP has implementado?*</label
              >
            </td>
            <td>
              <textarea
                name="cf_1490"
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 resize-none h-24 min-h-24 max-h-24"
              ></textarea>
            </td>
          </tr>
          <tr class="grid mb-2">
            <td>
              <label class="font-medium pl-2 text-textGray"
                >¿Cómo está conformado tu equipo de trabajo?*</label
              >
            </td>
            <td>
              <textarea
                name="cf_1594"
                required=""
                class="bg-inputGray text-gray-900 text-sm rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 resize-none h-24 min-h-24 max-h-24"
              ></textarea>
            </td>
          </tr>
          <tr class="flex items-center justify-between mb-2">
            <td>
              <label
                for="recibir-correos"
                class="pl-2 font-medium text-textGray"
                >Recibir Correos de Microsip</label
              >
            </td>
            <td>
              <input
                type="hidden"
                name="cf_1325"
                data-label=""
                value="0"
              /><input
                type="checkbox"
                name="cf_1325"
                data-label=""
                value="1"
                class="bg-inputGray text-gray-900 text-sm rounded-lg focus:ring-orange focus:border-orange block h-5 w-5 p-2.5 appearance-none checked:bg-orange"
                id="recibir-correos"
              />
            </td>
          </tr>
          <tr>
            <td>
              <select
                name="cf_1141"
                data-label="label:Procedencia"
                required=""
                hidden=""
              >
                <option value="">Seleccionar valor</option>
                <option value="Web">Web</option>
                <option value="Web - Partner" selected="">Web - Partner</option>
                <option value="Facebook">Facebook</option>
                <option value="Chat">Chat</option>
                <option value="Tel">Tel</option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
                <option value="Campaña FB">Campaña FB</option>
                <option value="Campaña FB Retargeting">
                  Campaña FB Retargeting
                </option>
                <option value="Campaña Noreste">Campaña Noreste</option>
                <option value="Campaña Preventa">Campaña Preventa</option>
                <option value="Campaña Retargeting">Campaña Retargeting</option>
                <option value="Talk Show">Talk Show</option>
                <option value="En Persona">En Persona</option>
                <option value="Email Mkt">Email Mkt</option>
                <option value="Expo Ferretera 2021">Expo Ferretera 2021</option>
                <option value="Expo Ferretera 2022">Expo Ferretera 2022</option>
                <option value="Expo Ferretera 2023">Expo Ferretera 2023</option>
                <option value="Whatsapp Business">Whatsapp Business</option>
                <option value="Campaña CFDI">Campaña CFDI</option>
                <option value="Campaña FB Golfo">Campaña FB Golfo</option>
                <option value="Campaña Preventa 2023">
                  Campaña Preventa 2023
                </option>
                <option value="Campaña Preventa 2024">
                  Campaña Preventa 2024
                </option>
                <option value="BNI">BNI</option>
                <option value="Campaña generación 2024">
                  Campaña generación 2024
                </option>
                <option value="Blog - Backlink">Blog - Backlink</option>
                <option value="Sin Información">Sin Información</option>
                <option value="Otro">Otro</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>
              <select name="cf_1139" data-label="label:Estatus" hidden="">
                <option value="">Seleccionar valor</option>
                <option value="Sin iniciar" selected="">Sin iniciar</option>
                <option value="Canalizado">Canalizado</option>
                <option value="Contactado">Contactado</option>
                <option value="Cotizado">Cotizado</option>
                <option value="Stand By">Stand By</option>
                <option value="Convertido">Convertido</option>
                <option value="Perdido">Perdido</option>
                <option value="Perdido - Sin Inf">Perdido - Sin Inf</option>
                <option value="Falso">Falso</option>
                <option value="NA">NA</option>
                <option value="Sin Información">Sin Información</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>
              <select name="cf_1339" data-label="label:Tipo" hidden="">
                <option value="">Seleccionar valor</option>
                <option value="Prospecto a cliente">Prospecto a cliente</option>
                <option value="Usuario final">Usuario final</option>
                <option value="Partner Microsip">Partner Microsip</option>
                <option value="Prospecto a Partner" selected="">
                  Prospecto a Partner
                </option>
                <option value="Lead duplicado">Lead duplicado</option>
                <option value="Ofrecieron Servicios">
                  Ofrecieron Servicios
                </option>
                <option value="Pruebas">Pruebas</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>
              <select name="cf_1428" data-label="label:Finalidad" hidden="">
                <option value="">Seleccionar valor</option>
                <option value="Compra">Compra</option>
                <option value="Distribución" selected="">Distribución</option>
                <option value="Soporte">Soporte</option>
                <option value="Error">Error</option>
                <option value="Bolsa de trabajo">Bolsa de trabajo</option>
                <option value="Donativo">Donativo</option>
                <option value="Sugerencia">Sugerencia</option>
                <option value="Otro">Otro</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>
              <input
                type="hidden"
                name="cf_1317"
                data-label=""
                value="https://www.microsip.com/quiero-ser-partner"
              />
            </td>
          </tr>
        </tbody>
      </table>
      <script
        src="https://www.google.com/recaptcha/api.js?hl=es"
        async
        defer
      ></script>
      <div
        class="g-recaptcha"
        data-sitekey="6LfXMKsZAAAAACGRuPAKqie7am6XkoKq0-tZz-aJ"
      ></div>
      <input
        type="hidden"
        id="captchaUrl"
        value="https://microsipmtz.mantic360.net/modules/Settings/Webforms/actions/CheckCaptcha.php"
      /><input
        type="hidden"
        name="recaptcha_validation_value"
        id="recaptcha_validation_value"
        value="false"
      /><input
        type="submit"
        value="Enviar"
        class="bg-orange hover:bg-orange-700 text-white font-bold py-2 px-4 rounded-lg mt-2 w-full appearance-none cursor-pointer hover:shadow-md transition-all duration-300"
      />
    </form>
    <script type="text/javascript">
      var _form_active = "";
      window.onload = function () {
        var N = navigator.appName,
          ua = navigator.userAgent,
          tem;
        var M = ua.match(
          /(opera|chrome|safari|firefox|msie)\/?\s*(\.?\d+(\.\d+)*)/i
        );
        if (M && (tem = ua.match(/version\/([\.\d]+)/i)) != null) M[2] = tem[1];
        M = M ? [M[1], M[2]] : [N, navigator.appVersion, "-?"];
        var browserName = M[0];
        var _forms = document.getElementsByClassName("__vtigerWebForm");
        for (var i = 0; i < _forms.length; i++) {
          var form = _forms[i];
          let inputs = form.elements;
          form.onsubmit = function () {
            let _formid = "#" + this.id;
            let _formidname = this.id;
            _form_active = this.id;
            var required = [],
              att,
              val;
            for (var i = 0; i < inputs.length; i++) {
              att = inputs[i].getAttribute("required");
              val = inputs[i].value;
              type = inputs[i].type;
              if (type == "email") {
                if (val != "") {
                  var elemLabel = inputs[i].getAttribute("label");
                  var emailFilter =
                    /^[_/a-zA-Z0-9]+([!"#$%&()*+,./:;<=>?\^_`{|}~-]?[a-zA-Z0-9/_/-])*@[a-zA-Z0-9]+([\_\-\.]?[a-zA-Z0-9]+)*\.([\-\_]?[a-zA-Z0-9])+(\.?[a-zA-Z0-9]+)?$/;
                  var illegalChars = /[\(\)\<\>\,\;\:\"\[\]]/;
                  if (!emailFilter.test(val)) {
                    alert(
                      "For " +
                        elemLabel +
                        " field please enter valid email address"
                    );
                    return false;
                  } else if (val.match(illegalChars)) {
                    alert(elemLabel + " field contains illegal characters");
                    return false;
                  }
                }
              }
              if (att != null) {
                if (val.replace(/^\s+|\s+$/g, "") == "") {
                  required.push(inputs[i].getAttribute("label"));
                }
              }
            }
            if (required.length > 0) {
              alert("The following fields are required: " + required.join());
              return false;
            }
            var numberTypeInputs =
              document.querySelectorAll("input[type=number]");
            for (var i = 0; i < numberTypeInputs.length; i++) {
              val = numberTypeInputs[i].value;
              var elemLabel = numberTypeInputs[i].getAttribute("label");
              var elemDataType = numberTypeInputs[i].getAttribute("datatype");
              if (val != "") {
                if (elemDataType == "double") {
                  var numRegex = /^[+-]?\d+(\.\d+)?$/;
                } else {
                  var numRegex = /^[+-]?\d+$/;
                }
                if (!numRegex.test(val)) {
                  alert(
                    "For " + elemLabel + " field please enter valid number"
                  );
                  return false;
                }
              }
            }
            var dateTypeInputs = document.querySelectorAll("input[type=date]");
            for (var i = 0; i < dateTypeInputs.length; i++) {
              dateVal = dateTypeInputs[i].value;
              var elemLabel = dateTypeInputs[i].getAttribute("label");
              if (dateVal != "") {
                var dateRegex =
                  /^[1-9][0-9]{3}-(0[1-9]|1[0-2]|[1-9]{1})-(0[1-9]|[1-2][0-9]|3[0-1]|[1-9]{1})$/;
                if (!dateRegex.test(dateVal)) {
                  alert(
                    "For " +
                      elemLabel +
                      " field please enter valid date in required format"
                  );
                  return false;
                }
              }
            }
            var inputElems = document.getElementsByTagName("input");
            var totalFileSize = 0;
            for (var i = 0; i < inputElems.length; i++) {
              if (inputElems[i].type.toLowerCase() === "file") {
                var file = inputElems[i].files[0];
                if (typeof file !== "undefined") {
                  var totalFileSize = totalFileSize + file.size;
                }
              }
            }
            if (totalFileSize > 52428800) {
              alert("Maximum allowed file size including all files is 50MB.");
              return false;
            }
            var getStatus = false;
            var recaptchaValidationValue = document.querySelector(
              _formid + " .g-recaptcha .g-recaptcha-response"
            ).value;
            var captchaUrl = document.querySelector(
              _formid + " #captchaUrl"
            ).value;
            var url =
              captchaUrl +
              "?form=" +
              _formidname +
              "&recaptcha_response=" +
              recaptchaValidationValue;
            url = url + "&callback=JSONPCallback";
            jsonp.fetch(url);
            if (getStatus == false) {
              return false;
            }
          };
        }
      };
      var jsonp = {
        callbackCounter: 0,
        fetch: function (url) {
          url = url + "&callId=" + this.callbackCounter;
          var scriptTag = document.createElement("SCRIPT");
          scriptTag.src = url;
          scriptTag.async = true;
          scriptTag.id = "JSONPCallback_" + this.callbackCounter;
          scriptTag.type = "text/javascript";
          document.getElementsByTagName("HEAD")[0].appendChild(scriptTag);
          this.callbackCounter++;
        },
      };
      function JSONPCallback(data) {
        if (data.result.success == true) {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = true;
          var form = document.getElementById(_form_active);
          form.submit();
        } else {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = false;
          alert("Bot detectado");
        }
        var element = document.getElementById(
          "JSONPCallback_" + data.result.callId
        );
        element.parentNode.removeChild(element);
      }
    </script>
  </body>
</html>
